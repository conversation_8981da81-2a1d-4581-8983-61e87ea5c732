import { Redirect } from '@benzinga/content-manager';
import { CONTENT_LANGUAGE } from '@benzinga/translate';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export const redirectMiddleware = async (req: NextRequest): Promise<NextResponse> => {
  const { host, pathname } = req.nextUrl;

  // Derive language from host
  const origin = host.split('.')[0];
  const lang = CONTENT_LANGUAGE[origin as keyof typeof CONTENT_LANGUAGE] || 'en-US';

  const redirectUrl = `https://www.benzinga.com/lavapress/api/redirects?url=${requestPath(pathname)}`;

  try {
    // Validate the URL for safety
    if (!isValidRedirectUrl(req.url)) {
      return NextResponse.redirect(new URL('/', req.url));
    }

    const splitPathname = pathname.split('/').filter(Boolean); // Remove empty segments

    // Skip processing for Next.js internals, assets, or root paths
    if (pathname.match('_next/|next-assets/|((?<!.)([^.])+/api/|api/)') || pathname === '' || pathname === '/') {
      const res = NextResponse.next();
      res.headers.set('Content-Language', lang);
      return res;
    }

    // Handle "quote" paths
    if (splitPathname[0] === 'quote') {
      return handleQuotePath(splitPathname, req);
    }

    // Redirect logic based on external API
    const data = await fetch(redirectUrl, {
      headers: {
        Accept: 'application/json',
      },
    });

    const response: Redirect = await data.json();

    if (response?.redirect) {
      const redirectUrl = new URL(normalizePath(response.redirect), req.url);
      if (!isValidRedirectUrl(redirectUrl.toString())) {
        return NextResponse.redirect(new URL('/', req.url));
      }
      return NextResponse.redirect(redirectUrl);
    }

    // Default response with Content-Language header
    const res = NextResponse.next();
    res.headers.set('Content-Language', lang);
    return res;
  } catch (error) {
    console.error('Middleware error:', error);
    console.log('Redirect URL:', redirectUrl);
    const res = NextResponse.next();
    res.headers.set('Content-Language', lang);
    return res;
  }
};

/**
 * Handle "quote" specific paths
 */
const handleQuotePath = (splitPathname: string[], req: NextRequest): NextResponse => {
  const [_, symbol, currency] = splitPathname;

  // Reject overly long symbols
  if (symbol?.length > 30) {
    return NextResponse.rewrite(new URL('/not-found', req.url), { status: 404 });
  }

  // Handle crypto quotes (e.g., $BTC -> BTC-USD)
  if (symbol?.[0] === '$') {
    const newSymbol = symbol.replace('$', '') + '-USD';
    const newPathname = `/quote/${newSymbol}${req.nextUrl.search}`;
    return NextResponse.redirect(new URL(newPathname, req.url), { status: 301 });
  }

  // Handle crypto USD pairs (e.g., BTC/USD -> BTC-USD)
  if (currency?.toUpperCase() === 'USD') {
    const newSymbol = `${symbol}-${currency}`.toUpperCase();
    const newPathname = `/quote/${newSymbol}${req.nextUrl.search}`;
    return NextResponse.redirect(new URL(newPathname, req.url), { status: 301 });
  }

  // Rewrite "quote" to "quotev2"
  const rewriteUrl = new URL(req.url.replace('/quote', '/quotev2'));
  if (!isValidRedirectUrl(rewriteUrl.toString())) {
    return NextResponse.rewrite(new URL('/', req.url));
  }
  return NextResponse.rewrite(rewriteUrl);
};

/**
 * Normalize paths to start with a single "/"
 */
const normalizePath = (path: string): string => {
  return path.startsWith('/') ? path : `/${path}`;
};

/**
 * Extract request path from URL
 */
const requestPath = (nextUrlPath: string): string => {
  return nextUrlPath.startsWith('/') ? nextUrlPath.slice(1) : nextUrlPath;
};

/**
 * Validate URL for path traversal and safety
 */
const isValidRedirectUrl = (url: string): boolean => {
  try {
    const parsedUrl = new URL(url);

    // Decode the pathname to catch encoded traversal attempts or invalid characters
    const decodedPathname = decodeURIComponent(parsedUrl.pathname);

    // Check for path traversal attempts or invalid protocols
    if (
      // decodedPathname.includes('..') ||
      // decodedPathname.includes('//') ||
      // /%2e/i.test(decodedPathname) || // Encoded dot
      // /%2f/i.test(decodedPathname) || // Encoded slash
      !['http:', 'https:'].includes(parsedUrl.protocol)
    ) {
      console.warn(`Path traversal attempt detected in: ${decodedPathname}`);
      return false;
    }

    // Allow encoded square brackets only if they are part of a dynamic route ([...slug])
    const isDynamicRoute = /\[.*?\]/.test(decodedPathname);
    if (!isDynamicRoute && (decodedPathname.includes('%5B') || decodedPathname.includes('%5D'))) {
      console.warn(`Invalid encoded character detected: ${decodedPathname}`);
      return false;
    }

    return true;
  } catch (error) {
    console.warn(`URL parsing error: ${error}`);
    return false;
  }
};
