import { runningServerSide, runningClientSide } from '@benzinga/utils';
import { Subscribable } from '@benzinga/subscribable';
import { User } from '@benzinga/session';
import { MetaProps, UserSubscription, ChartbeatUserType } from './entities';
import { Session } from '@benzinga/session';
import { AnalyticsBrowser } from '@segment/analytics-next';
import { TrackingEnvironment } from './environment';
import { appEnvironment, appName, getValueFromCookie } from '@benzinga/utils';

declare global {
  interface Window {
    chartbeatLoaded?: boolean;
    loadChartbeat?: (userType: ChartbeatUserType) => void;
    _sf_async_config?: {
      authors: string;
      domain: string;
      flickerControl: boolean;
      sections: string;
      uid: number | string;
      useCanonical: boolean;
      useCanonicalDomain: boolean;
    };
    // ToDo: Cleanup section
    pSUPERFLY?: {
      virtualPage: ({
        authors,
        path,
        referrer,
        sections,
        title,
      }: {
        authors?: string | undefined;
        path?: string;
        referrer?: string | undefined;
        sections?: string;
        title?: string;
      }) => void;
    };
  }
}

interface TrackingLogMessage {
  category: string;
  message: string;
  data?: unknown;
  type: 'tracking:log_message';
}

interface VisitInfo {
  count: number;
  week: number;
  day_of_year: number;
}

export type TrackingManagerEvent = TrackingLogMessage;

export type AuthEventAction = 'login' | 'logout' | 'register' | 'forgot-password' | 'reset-password';
export type LinkEventAction = 'view' | 'hover' | 'click';
export type CampaignEventAction = 'view' | 'click';
export type GPTEventAction = 'prompt';
export type ChatEventAction =
  | 'delete_message'
  | 'error'
  | 'flag'
  | 'mute'
  | 'unmute'
  | 'new_message'
  | 'edit_message'
  | 'click'
  | 'report'
  | 'reaction'
  | 'reply'
  | 'view_channel'
  | 'view_message';
export type CheckoutEventAction = 'view';
export type CommentEventAction =
  | 'new_message'
  | 'reaction'
  | 'reply'
  | 'delete_message'
  | 'view_message'
  | 'open_drawer'
  | 'auth';
export type FormEventAction = 'view' | 'focus_input' | 'blur_input' | 'filled_input' | 'submit';
export type DownloadEventAction = 'view' | 'click';
export type ModalEventAction = 'view' | 'submit' | 'close';
export type NoteEventAction = 'view' | 'add' | 'delete';
export type HeadlineEventAction = 'viewed_inline';
export type NotificationEventAction = 'view' | 'click' | 'dismiss' | 'add' | 'remove' | 'subscribe';
export type PageEventAction = 'view' | 'scroll' | 'click' | 'load_more';
export type PaywallEventAction = 'view' | 'click' | 'login' | 'register' | 'close' | 'submit' | 'checkout';
export type ProfileEventAction = 'view' | 'edit' | 'update' | 'delete' | 'reset_password' | 'change_password';
export type SearchEventAction = 'blur' | 'focus' | 'search' | 'result_click' | 'results' | 'filter' | 'cancel';
export type TickerEventAction = 'view' | 'click' | 'hover';
export type WnstnEventAction =
  | 'follow_up_wnstn_widget_click'
  | 'follow_up_wnstn_widget_view'
  | 'asset_wnstn_widget_view'
  | 'asset_wnstn_widget_click';
export type UserLocationEventAction = 'detect' | 'update';
export type TableEventAction =
  | 'view'
  | 'row_click'
  | 'sort'
  | 'filter'
  | 'load_more'
  | 'change_page'
  | 'change_page_size';
export type VideoEventAction =
  | 'view'
  | 'play'
  | 'pause'
  | 'seek'
  | 'end'
  | 'mute'
  | 'unmute'
  | 'fullscreen'
  | 'exit_fullscreen'
  | 'volume_change';
export type SquawkWidgetEventAction =
  | 'connected'
  | 'connection_error'
  | 'stream_error'
  | 'disconnected'
  | 'mute'
  | 'unmute'
  | 'volume_up'
  | 'volume_down';
export type WidgetEventAction =
  | 'view'
  | 'click'
  | 'load_more'
  | 'save_export'
  | 'refresh_data'
  | 'error'
  | 'status'
  | 'add_filter'
  | 'remove_filter'
  | 'update_setting'
  | 'sort'
  | 'add_to_workspace'
  | 'popout'
  | 'delete_from_workspace'
  | 'close'
  | SquawkWidgetEventAction;
export type WatchlistEventAction =
  | 'view'
  | 'view_all'
  | 'new_watchlist'
  | 'delete_watchlist'
  | 'add_symbol'
  | 'delete_symbol'
  | 'update_symbol'
  | 'add_note'
  | 'enable_alert'
  | 'disable_alert'
  | 'import'
  | 'delete_note';
export type SettingsEventAction =
  | 'view'
  | 'update'
  | 'delete'
  | 'add'
  | 'enable'
  | 'disable'
  | 'change'
  | 'reset'
  | 'select'
  | 'deselect';
export type OnboardingEventAction =
  | 'view'
  | 'click'
  | 'submit'
  | 'close'
  | 'next'
  | 'back'
  | 'complete_step'
  | 'skip'
  | 'start'
  | 'complete_all';
export type ErrorEventAction = 'emit';
export type LayoutEventAction =
  | 'view'
  | 'error'
  | 'load_workspace'
  | 'load_workspace_preset'
  | 'add_workspace'
  | 'delete_workspace'
  | 'update_workspace'
  | 'add_to_workspace'
  | 'delete_from_workspace'
  | 'load_layout'
  | 'add_layout'
  | 'reset_layout'
  | 'delete_layout'
  | 'update_layout'
  | 'add_to_layout'
  | 'delete_from_layout'
  | 'pane_resize';

export type SubscriptionEventAction = 'added' | 'removed' | 'updated' | 'cancelled' | 'renewed' | 'paused';

export type AuthEventProperties = { [key: string]: any };

export type AnyEventAction =
  | AuthEventAction
  | LinkEventAction
  | CampaignEventAction
  | ChatEventAction
  | CheckoutEventAction
  | CommentEventAction
  | DownloadEventAction
  | ErrorEventAction
  | FormEventAction
  | GPTEventAction
  | LayoutEventAction
  | ModalEventAction
  | NotificationEventAction
  | OnboardingEventAction
  | PageEventAction
  | PaywallEventAction
  | ProfileEventAction
  | SearchEventAction
  | SettingsEventAction
  | SubscriptionEventAction
  | TableEventAction
  | TickerEventAction
  | UserLocationEventAction
  | VideoEventAction
  | WatchlistEventAction
  | WidgetEventAction
  | WnstnEventAction
  | HeadlineEventAction;

export type AuthEventType = 'email' | 'google' | 'apple' | 'microsoft' | 'google-one-tap';

export const GA4_SESSION_COOKIE = '_ga_ZVH0G4BQKE'; // _ga_{property_id}

export class TrackingManager extends Subscribable<TrackingManagerEvent> {
  protected meta = {} as MetaProps | null;
  protected subscriptions = [] as UserSubscription[] | null | undefined;
  protected user = { email: 'unknown' } as User | null | undefined;
  private warnedNoIntercom = false;
  private session: Session;
  private uniqueId: number | null = null;
  private context = {
    isIncognito: null,
    isPaywalled: false,
  } as {
    isIncognito: null | boolean;
    isPaywalled: boolean;
  };
  private logEnabled = false;
  private localStorageKey = 'pwdata';

  constructor(session: Session) {
    super();
    this.uniqueId = new Date().getTime() + Math.floor(Math.random() * 1000000);
    this.session = session;
    if (runningClientSide()) {
      if (session.getEnvironment(TrackingEnvironment).segmentKey) {
        global.analytics = AnalyticsBrowser.load({ writeKey: session.getEnvironment(TrackingEnvironment).segmentKey });
      }
    }
  }

  public static getName = () => 'benzinga-tracking';

  public updateContext(context: { isPaywalled?: boolean; isIncognito?: boolean | null }) {
    this.context = { ...this.context, ...context };
  }

  public setMeta(metaData: MetaProps | null) {
    this.meta = metaData;
  }

  public setUser(user: User | null | undefined) {
    this.user = user;
  }

  public setSubscriptions(subscriptions: UserSubscription[] | null | undefined) {
    this.subscriptions = subscriptions;
  }

  public identify(userId: number, properties: any) {
    // Segment
    if (global.analytics) {
      global.analytics.identify(userId, properties);
    }

    // Google Tag Manager
    if ((window as any).gtagAnalytics) {
      (window as any).gtagAnalytics.identify(userId, properties);
    }
  }

  public setLogging(logEnabled: boolean) {
    this.logEnabled = logEnabled;
  }

  public logout() {
    if (runningServerSide()) return;
    (window as any).analytics.reset();
  }

  public trackAuthEvent(action: AuthEventAction, properties: { auth_type?: AuthEventType; is_paywalled?: boolean }) {
    const { auth_type, is_paywalled } = properties;
    this.segmentTrack('auth_event', action, {
      auth_type,
      is_paywalled: is_paywalled ?? this.context.isPaywalled,
    });
  }

  public trackPageEvent(
    action: PageEventAction,
    properties: { page_tab?: string; page_section?: string; scroll_depth?: number },
  ) {
    const { page_section, page_tab } = properties;
    this.segmentTrack('page_event', action, {
      is_incognito: this.context.isIncognito,
      page_section,
      page_tab,
    });
  }

  public trackTableEvent(
    action: TableEventAction,
    table_id: string,
    table_type: string,
    properties: { page_num?: number; page_size?: number },
  ) {
    const { page_num, page_size } = properties;
    this.segmentTrack('table_event', action, {
      page_num, // the current page number
      page_size, // the number of rows per page
      table_id, // the id of the table
      table_type, // the type of table (e.g. earnings, ratings)
    });
  }

  public trackDownloadEvent(action: DownloadEventAction, properties: { asset_id?: string; asset_url?: string }) {
    const { asset_id, asset_url } = properties;
    this.segmentTrack('download_event', action, {
      asset_id, // the id of the asset
      asset_url, // the url of the asset
    });
  }

  public trackHeadlineEvent(action: HeadlineEventAction, properties: { nodeID: string }) {
    const { nodeID } = properties;
    this.segmentTrack('page_event', action, {
      node_id: nodeID,
    });
  }

  public trackCampaignEvent(
    action: CampaignEventAction,
    properties: {
      campaign_id?: string;
      label?: string;
      non_interaction?: boolean;
      partner_id?: string;
      sponsored?: string;
      tag?: string;
      unit_type?: string;
      url?: string;
      additionalProperties?: {
        adType?: string;
        ad?: string;
        campaign?: string;
      };
    },
  ) {
    const { additionalProperties, campaign_id, label, non_interaction, partner_id, sponsored, tag, unit_type, url } =
      properties;
    this.segmentTrack('campaign_event', action, {
      campaign_id, // the id of the campaign
      label, // the label for this campaign
      non_interaction,
      partner_id, // the partner id for this campaign, use benzinga if internal
      sponsored, // whether the campaign is sponsored
      tag, // the tag for this campaign
      unit_type, // the type of unit (e.g. com-top-banner, campaignify-top, etc)
      url, // the url of the campaign
      ...(additionalProperties || {}),
    });
  }

  public trackUserLocationEvent(action: UserLocationEventAction, properties: { timezone?: string }) {
    const { timezone } = properties;
    this.segmentTrack('user_location_event', action, {
      timezone, // user's timezone
    });
  }

  public trackCheckoutEvent(
    action: CheckoutEventAction,
    properties: { product_id?: string; package_id?: string; checkout_type?: string },
  ) {
    const { checkout_type, package_id, product_id } = properties;
    this.segmentTrack('checkout_event', action, {
      checkout_type, // the type of checkout (e.g. clickfunnels, cartflow, etc)
      package_id, // the package id being purchased
      product_id, // the product id being purchased
    });
  }

  public trackWnstnEvent(
    action: WnstnEventAction,
    properties: { node_id?: string; question?: string; page_link?: string },
  ) {
    const { node_id, page_link, question } = properties;
    this.segmentTrack('wnstn_widget', action, {
      node_id, // article node id if placed on article page
      page_link, // page link
      question, // selected question from wnstn widget
    });
  }

  public trackSearchEvent(
    action: SearchEventAction,
    search_type: string,
    properties: { query?: string; symbol?: string },
  ) {
    const { query } = properties;
    this.segmentTrack('search_event', action, {
      query, // the search query
      search_type, // the type of search (e.g. news, symbol, etc)
    });
  }

  public trackNoteEvent(action: NoteEventAction, properties: { symbol: string }) {
    const { symbol } = properties;
    this.segmentTrack('note_event', action, {
      symbol, // the symbol being noted
    });
  }

  public trackLinkEvent(
    action: LinkEventAction,
    properties: {
      value?: string;
      link_action?: string;
      link_id?: string;
      link_type?: string;
      link_url?: string;
    },
  ) {
    const { link_action, link_id, link_type, link_url, value } = properties;
    this.segmentTrack('button_event', action, {
      link_action, // onClick action
      link_id, // the id of the button
      link_type, // the type of button (e.g. social, cta, show_more, etc)
      link_url, // url if button redirects to another page
      value, // the text/icon on the button
    });
  }

  public trackChatEvent(
    action: ChatEventAction,
    properties: {
      button_id?: string;
      button_value?: string;
      channel_id?: string;
      message_id?: string;
      quoted_id?: string;
      thread_id?: string;
      product_id?: string;
      reaction?: string;
      muted_user_id?: string;
      report_message?: string;
      user_id?: string;
      error_message?: string;
    },
  ) {
    const {
      button_id,
      button_value,
      channel_id,
      error_message,
      message_id,
      muted_user_id,
      product_id,
      quoted_id,
      reaction,
      report_message,
      thread_id,
      user_id,
    } = properties;
    this.segmentTrack('chat_event', action, {
      button_id, // the id of the button
      button_value, // the text/icon on the button
      channel_id, // channel id where chat is occuring
      error_message, // error message if any
      message_id, // the id of the message
      muted_user_id, // the user id being muted
      product_id, // which product is the chat occuring under
      quoted_id, // the id of the quoted message
      reaction, // the reaction being added
      report_message, // the type of report (e.g. spam, inappropriate, etc)
      thread_id, // the id of the thread
      user_id: user_id, // chat id for user
    });
  }

  public trackCommentEvent(
    action: CommentEventAction,
    properties: {
      page_type?: string;
      page_tab?: string;
      page_section?: string;
    },
  ) {
    const { page_section, page_tab, page_type } = properties;
    this.segmentTrack('comment_event', action, {
      page_section,
      page_tab,
      page_type,
    });
  }

  public trackNotificationEvent(
    action: NotificationEventAction,
    properties: { notification_type?: string; notification_id?: string },
  ) {
    const { notification_id, notification_type } = properties;
    this.segmentTrack('notification_event', action, {
      notification_id, // the id of the notification
      notification_type, // the type of notification (e.g. watchlist_alert, promo, etc)
    });
  }

  public trackProfileEvent(action: ProfileEventAction) {
    this.segmentTrack('profile_event', action, {});
  }

  public trackWatchlistEvent(action: WatchlistEventAction, properties: { symbol?: string; watchlist_id?: string }) {
    const { symbol, watchlist_id } = properties;
    this.segmentTrack('watchlist_event', action, {
      symbol, // the symbol being modified
      watchlist_id, // the watchlist id
    });
  }

  public trackTickerEvent(action: TickerEventAction, properties: { symbol?: string; source?: string }) {
    const { source, symbol } = properties;
    this.segmentTrack('ticker_event', action, {
      source, // the source of the ticker event (e.g. watchlist, article, etc)
      ticker: symbol, // the symbol being interacted with
    });
  }

  public trackModalEvent(
    action: ModalEventAction,
    properties: { modal_id?: string; allow_close?: boolean; modal_type?: string },
  ) {
    const { allow_close, modal_id, modal_type } = properties;
    this.segmentTrack('modal_event', action, {
      allow_close, // whether the modal can be closed
      modal_id, // the id of the modal
      modal_type, // the type of modal (e.g. ok_cancel, prompt, etc)
    });
  }

  public trackVideoEvent(
    action: VideoEventAction,
    properties: { partner_id?: string; video_id?: string; video_type?: string },
  ) {
    const { partner_id, video_id, video_type } = properties;
    this.segmentTrack('video_event', action, {
      partner_id, // the partner id for this video, use benzinga if internal
      video_id, // the id of the video
      video_type, // the type of video (e.g. youtube, vimeo, etc)
    });
  }

  public trackPaywallEvent(
    action: PaywallEventAction,
    properties: { paywall_id?: string; paywall_type?: string; placement?: string },
  ) {
    const { paywall_id, paywall_type, placement } = properties;
    this.segmentTrack('paywall_event', action, {
      paywall_id,
      paywall_type,
      placement,
    });
  }

  public trackFormEvent(
    action: FormEventAction,
    form_id: string,
    properties: { form_id?: string; form_type?: string },
  ) {
    const { form_type } = properties;
    this.segmentTrack('form_event', action, {
      form_id,
      form_type,
    });
  }

  public trackWidgetEvent(
    action: WidgetEventAction,
    widget_type?: string,
    properties?: {
      asset_type?: string;
      error_message?: string;
      period?: string;
      symbol?: string;
      filter_id?: string;
      filter_value?: string;
      setting_id?: string;
      setting_value?: string;
      button_id?: string;
      button_value?: string;
      widget_sub_type?: string;
      widget_preset?: string;
      widget_filter?: any;
    },
  ) {
    const {
      asset_type,
      button_id,
      button_value,
      filter_id,
      filter_value,
      period,
      setting_value,
      symbol,
      widget_filter,
      widget_preset,
      widget_sub_type,
    } = properties || {};
    this.segmentTrack('widget_event', action, {
      asset_type,
      button_id,
      button_value,
      filter_id,
      filter_value,
      period,
      setting_value,
      symbol,
      widget_filter,
      widget_preset,
      widget_sub_type,
      widget_type,
    });
  }

  public trackLayoutEvent(
    action: LayoutEventAction,
    properties: {
      error_message?: string;
      layout_id?: string;
      layout_type?: string;
      preset?: string;
      widget_id?: string;
      widget_type?: string;
      workspace_count?: number;
      workspace_id?: string;
      property_change?: string;
    },
  ) {
    const { error_message, layout_id, layout_type, preset, widget_id, widget_type, workspace_count, workspace_id } =
      properties;
    this.segmentTrack('layout_event', action, {
      error_message,
      layout_id,
      layout_type,
      preset,
      widget_id,
      widget_type,
      workspace_count,
      workspace_id,
    });
  }

  public trackSettingsEvent(
    action: SettingsEventAction,
    properties: { setting_id?: string; setting_type?: string; setting_value?: string },
  ) {
    const { setting_id, setting_type, setting_value } = properties;
    this.segmentTrack('settings_event', action, {
      setting_id,
      setting_type,
      setting_value,
    });
  }

  public trackSubscriptionEvent(
    action: SubscriptionEventAction,
    properties: { interval?: string; package_id?: string; product_id?: string; price: number },
  ) {
    const { interval, package_id, price, product_id } = properties;
    this.segmentTrack('settings_event', action, {
      interval,
      package_id,
      price,
      product_id,
    });
  }

  public trackErrorEvent(action: ErrorEventAction, properties: { error_message?: string; user_id?: string }) {
    const { error_message, user_id } = properties;
    this.segmentTrack('error_event', action, {
      error_message,
      user_id,
    });
  }

  public trackGPTEvent(
    action: GPTEventAction,
    properties: {
      prompt?: string;
      prompt_author?: string;
      thread_id?: string;
      reply?: string;
      reply_author?: string;
    },
  ) {
    const { prompt, prompt_author, reply, reply_author, thread_id } = properties;
    this.segmentTrack('gpt_event', action, {
      prompt,
      prompt_author,
      reply,
      reply_author,
      thread_id,
    });
  }

  public trackOnboardingEvent(
    action: OnboardingEventAction,
    properties: { onboarding_step?: string; onboarding_step_value?: string },
  ) {
    const { onboarding_step, onboarding_step_value } = properties;
    this.segmentTrack('onboarding_event', action, {
      onboarding_step,
      onboarding_step_value,
    });
  }

  // Update the visit count and current week in local storage
  protected updateVisitInfo(): void {
    const dayNow = new Date();
    const start = new Date(dayNow.getFullYear(), 0, 0);
    const diff = dayNow.getTime() - start.getTime();
    const oneDay = 1000 * 60 * 60 * 24;
    const dayOfYear = Math.floor(diff / oneDay);

    let userVisitInfo: VisitInfo = {
      count: 1,
      day_of_year: dayOfYear,
      week: this.getWeekNumber(new Date(), 3),
    };

    try {
      const lastVisitInfo = JSON.parse(localStorage.getItem(this.localStorageKey) || '{}') as VisitInfo;
      // if (lastVisitInfo && lastVisitInfo.day_of_year === userVisitInfo.day_of_year) {
      if (lastVisitInfo && lastVisitInfo.week === userVisitInfo.week) {
        lastVisitInfo.count += 1;
        userVisitInfo = lastVisitInfo;
      }
    } catch (e) {
      console.error(e);
    }
    localStorage.setItem(this.localStorageKey, JSON.stringify(userVisitInfo));
  }

  protected getArticleTemplateType = (): 'new' | 'old' => {
    const override = new URLSearchParams(window.location.search).get('template') as 'new' | 'old';
    if (override && ['new', 'old'].includes(override)) return override;
    return 'new';
  };

  protected getValueFromCookie = (key: string): string | undefined => {
    if (typeof document !== 'undefined') {
      return document.cookie
        .split('; ')
        .find(cookie => cookie.startsWith(key))
        ?.split('=')[1];
    }
    return undefined;
  };

  protected doSegmentTrack = async (
    event: string,
    action: AnyEventAction,
    properties: { [key: string]: any },
    _benzingaId = -1,
  ) => {
    if (runningServerSide()) return;
    const benzingaId = _benzingaId > -1 ? _benzingaId : this.getValueFromCookie('benzinga_uid');
    const eventData = {
      article_section: this.meta?.structuredData?.articleSection || undefined,
      author: this.meta?.author || undefined,
      author_id: this.meta?.authorId || undefined,
      author_type: this.meta?.authorType || undefined,
      author_url: this.meta?.authorURL || undefined,
      canonical: this.meta?.canonical || undefined,
      channels:
        this.meta?.dimensions?.channels
          ?.map(m => {
            return m.toString().replaceAll('"', '');
          })
          .join(', ') || '',
      client_id: getValueFromCookie('_ga') || undefined,
      content_type: this.meta?.dimensions?.contentType,
      date_created: this.meta?.dateCreated || undefined,
      date_updated: this.meta?.dateUpdated || undefined,
      debug_mode: 1,
      description: this.meta?.description || undefined,
      event_action: action,
      href_language: this.meta?.hrefLanguage || undefined,
      image: this.meta?.image || undefined,
      isbz_isbzpro: this.meta?.dimensions?.isBZisBZPRO || false,
      keywords:
        this.meta?.structuredData?.keywords
          ?.map(k => {
            return k.toString().replaceAll('"', '');
          })
          .join(', ') || '',
      language: this.meta?.language || undefined,
      mentions:
        this.meta?.structuredData?.mentions
          ?.map(m => {
            return m.toString().replaceAll('"', '');
          })
          .join(', ') || '',
      og_description: this.meta?.ogDescription || undefined,
      og_image: this.meta?.ogImage || undefined,
      og_title: this.meta?.ogTitle || undefined,
      og_type: this.meta?.ogType || undefined,
      page_location: window.location.href,
      page_referer: this.meta?.referer || document.referrer || undefined,
      page_type: this.meta?.pageType || undefined,
      release_version: process.env['RELEASE_VERSION'] || undefined,
      robots: this.meta?.robots || undefined,
      session_id: getValueFromCookie(GA4_SESSION_COOKIE) || undefined,
      short_url: this.meta?.shortUrl || undefined,
      sub_title: this.meta?.subTitle || undefined,
      symbol: this.meta?.symbol || undefined,
      template: this.meta?.nodeId ? this.getArticleTemplateType() : undefined,
      title: this.meta?.title || undefined,
      twitter_description: this.meta?.twitterDescription || undefined,
      twitter_image: this.meta?.twitterImage || undefined,
      twitter_title: this.meta?.twitterTitle || undefined,
      vertical: this.meta?.vertical
        ?.map(v => {
          return v.toString().replaceAll('"', '');
        })
        .join(', '),
    };

    const userData = {
      user_active_monthly_price: this.subscriptions?.reduce((acc, sub) => {
        return sub.status === 'active' && sub.interval === 'month' ? acc + parseInt(sub.finalPrice || '0') : acc;
      }, 0),
      user_active_one_time_price: this.subscriptions?.reduce((acc, sub) => {
        return sub.status === 'active' && sub.interval === 'onetime' ? acc + parseInt(sub.finalPrice || '0') : acc;
      }, 0),
      user_active_plan_ids:
        this.subscriptions
          ?.filter(sub => sub.status === 'active')
          .map(sub => sub.basePlan)
          .join(', ') || undefined,
      user_active_subscriptions: this.subscriptions?.filter(sub => sub.status === 'active').length || 0,
      user_active_yearly_price: this.subscriptions?.reduce((acc, sub) => {
        return sub.status === 'active' && sub.interval === 'year' ? acc + parseInt(sub.finalPrice || '0') : acc;
      }, 0),
      user_email_verified: this.user?.emailVerified || false,
      user_id: this.user && this.user?.accessType !== 'anonymous' ? this.user?.benzingaUid || benzingaId : undefined,
      user_logged_in: this.user?.email ? true : false,
      user_sms_verified: this.user?.smsVerified || false,
      user_total_subscriptions: this.subscriptions?.length || 0,
    };

    // TODO: Testing Sophi
    const sophiData = window?.['sophi']?.dataLayer;

    const props = {
      action: action,
      benzinga_id: this.user?.benzingaUid || benzingaId,
      event: event,
      event_data: {
        benzinga_id: this.user?.benzingaUid || benzingaId,
        ...properties,
        ...eventData,
        ...userData,
      },
      // Required Sophi fields from dataLayer
      sophi_context: sophiData?.context,
      sophi_experiments_code: sophiData?.experimentsCode,
      sophi_inputs: sophiData?.inputs,
      sophi_trace: sophiData?.trace,
      user_data: userData,
    };

    if (global.analytics) {
      if (event === 'page_event' && action === 'view') {
        if (this.meta?.nodeId) {
          // we only want to count the article views to increment actions
          this.updateVisitInfo();
        }

        // Google Tag Manager
        if ((window as any).gtagAnalytics) {
          (window as any).gtagAnalytics.trackPage(props.event_data.title || event, props);
        }
        // Send page event for use in Segment elsewhere - We'll convert the below page_event__view to a page event in GA4
        global.analytics.page(eventData.page_type, eventData.title, props);
      }
      global.analytics.track(event, props);

      if (event === 'page_event' && action === 'view') {
        if (!window?.chartbeatLoaded) {
          if (window?.loadChartbeat) {
            window.chartbeatLoaded = true;
            const userType: ChartbeatUserType =
              this.user?.accessType === 'subscribed'
                ? 'paid'
                : this.user?.accessType === 'anonymous'
                  ? 'anon'
                  : 'lgdin';
            window.loadChartbeat(userType);
          }
        } else {
          if (window?.pSUPERFLY) {
            // const tags = props.event_data.keywords || [];
            const channels = props.event_data.channels || [];
            let chartbeatSections = channels.toString() || '';
            const isMoneyApp = appEnvironment().isApp(appName.money);

            if (isMoneyApp) {
              chartbeatSections = props.event_data?.vertical || '';
            }

            window.pSUPERFLY.virtualPage({
              // These are likely not needed --------
              // data: props.event_data,
              // url: window.location.href,

              // Causing an issue, likely due to a hydration issue
              // referrer: props?.event_data?.page_referer ?? '',

              authors: props?.event_data?.author ?? 'Benzinga',
              path: window.location.pathname,
              sections: chartbeatSections,
              title: props?.event_data?.title ?? document?.title ?? '',
            });
          }
        }
      }
    }

    this.dispatch({
      category: event + '__' + action,
      data: props,
      message: global.analytics ? 'Event sent to Segment' : 'Event not sent to Segment',
      type: 'tracking:log_message',
    });
  };

  protected segmentTrack = (event: string, action: AnyEventAction, properties: { [key: string]: any }) => {
    // run tracking on the callback queue to allow for react to finish passing along page data on the microtask queue
    if (
      Object.prototype.hasOwnProperty.call(window, 'requestIdleCallback') &&
      typeof window.requestIdleCallback === 'function'
    ) {
      requestIdleCallback(() => {
        this.doSegmentTrack(event, action, properties);
      });
    } else {
      setTimeout(() => {
        this.doSegmentTrack(event, action, properties);
      }, 0);
    }
  };

  // Function to get the week number of the year
  private getWeekNumber(date: Date, startOfWeek: number): number {
    // startOfWeek should be a number from 0 to 6:
    // 0 - Sunday
    // 1 - Monday
    // 2 - Tuesday
    // 3 - Wednesday
    // 4 - Thursday
    // 5 - Friday
    // 6 - Saturday

    const oneJan = new Date(date.getFullYear(), 0, 1);
    // Adjust oneJan.getDay() to make the specified start day of the week the start of the week
    const dayOfWeek = (oneJan.getDay() - startOfWeek + 7) % 7; // Shifts the week start to the specified day
    return Math.ceil(((date.getTime() - oneJan.getTime()) / 86400000 + dayOfWeek) / 7);
  }

  private log(message: any) {
    if (this.logEnabled) {
      // tslint:disable-next-line:no-console
      console.log('TrackingManager:', message);
    }
  }
}
