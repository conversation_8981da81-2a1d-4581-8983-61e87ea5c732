'use client';
import React from 'react';
import classNames from 'classnames';
import styled from '@benzinga/themetron';

import {
  ArticleBlock,
  ArticleData,
  Campaigns,
  CampaignStrategy,
  checkIfShouldLoadCalendar,
  getCanonicalUrl,
  getPrimaryTickers,
  isSponsoredArticle,
  setDFPTargeting,
} from '@benzinga/article-manager';

import { AccountCreationCTA, ShareButtons } from '@benzinga/ui';
import { appEnvironment } from '@benzinga/utils';

import { AuthContainer } from '@benzinga/auth-ui';
import { ButtonVariant, ErrorBoundary } from '@benzinga/core-ui';
import { AnalyticsContext, VisiblePageTracker } from '@benzinga/analytics';

//import { Campaign } from './Campaign';
import { NewCampaign } from './NewCampaign';
import { ArticleIFrame } from './ArticleIFrame';
import { ArticleBlocks } from './ArticleBlocks';
import { ArticleHeadlineContent } from './ArticleHeadlineContent';
import { ArticleBodyContent } from './ArticleBody';
import { PostedIn } from './PostedIn';

import { ArticleFeaturedTicker, ArticlePageProps } from '../entities';
import { IsUserPaywalledReturn } from '@benzinga/user-context';
import { ArticleContentWrapper, whitelistedAuthors } from './ArticleLayoutMain';
import { LightBlueKeyPoints } from './KeyPoints';
import { TaboolaSettings } from './GetBelowArticlePartnerAdBlock';
import { NoFirstRender } from '@benzinga/hooks';
import { getPaywallContentType } from '../utils';
import { raptiveAdManager } from '@benzinga/ads-utils';

const ArticleImageGallery = React.lazy(() =>
  import('./ArticleImageGallery').then(module => ({
    default: module.ArticleImageGallery,
  })),
);
const ArticleFeaturedTickersList = React.lazy(() => import('./ArticleFeaturedTickersList'));
const KeyPoints = React.lazy(() => import('./KeyPoints'));
const ArticleCalendarWidget = React.lazy(() => import('./ArticleCalendar'));
const RelatedArticles = React.lazy(() => import('./RelatedArticles'));

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

const TaboolaPixelHeader = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.TaboolaPixelHeader };
  }),
);

const WNSTNWidget = React.lazy(() => import('@benzinga/ads').then(module => ({ default: module.WNSTNWidget })));

export interface ArticleLayoutMainProps extends Partial<ArticlePageProps> {
  articleData: ArticleData;
  articleScrollViewMoreLink?: string;
  baseUrl?: string;
  disablePartnerAdOnScroll?: boolean;
  hasAdLight: boolean;
  hideTopPanel?: boolean;
  isAmp?: boolean;
  isEditor: boolean;
  isBenzingaContributor: boolean;
  isBot: boolean;
  generatedByAi?: boolean;
  googleNewsUrlKey?: 'benzinga' | 'benzingaIndia';
  loadMoreButtonVariant?: ButtonVariant;
  isTaggedPressRelease?: boolean;
  isDraft?: boolean;
  taboolaSettings?: TaboolaSettings;
  relatedArticles?: ArticleData[];
  showAccountCreationCTA?: boolean;
  showAdvertiserDisclosure?: boolean;
  showApiText?: boolean;
  showFontAwesomeIcons?: boolean;
  showWhatsAppIcon?: boolean;
  executeReplaceHistory?: boolean;
  executePageviewEvent?: boolean;
  showPartnerAd?: boolean;
  campaign?: Campaigns;
  campaignStrategy?: CampaignStrategy;
  campaignTicker?: string;
  trackMeta?: boolean;
  trackPageView?: boolean;
  primis?: boolean;
  disableCampaign?: boolean;
  raptiveEnabled?: boolean;
  isNotPaywalled?: boolean;
  isPaywallActive?: boolean;
  paywall: IsUserPaywalledReturn;
  featuredTickers?: ArticleFeaturedTicker[];
  setShowPaywall?: (value: boolean) => void;
}

const copyright = `© ${new Date().getFullYear()} Benzinga.com. Benzinga does not provide investment advice. All rights reserved.`;

const MemoCampaign = React.memo(NewCampaign);

export const NewArticleLayoutMain: React.FC<ArticleLayoutMainProps> = React.memo(
  ({
    articleData,
    //articleIndex,
    campaigns,
    campaignSettings = {},
    campaignStrategy,
    campaignTicker,
    deviceType,
    executePageviewEvent = false,
    executeReplaceHistory = false,
    featuredTickers,
    followUpQuestions,
    generatedByAi = false,
    hasAdLight = false,
    isBot = true,
    isPaywallActive,
    isTemplate = false,
    layout,
    paywall,
    postedInVariant = 'default',
    rankingData,
    raptiveEnabled = false,
    relatedArticles,
    setShowPaywall,
    showAccountCreationCTA = true,
    showAdvertiserDisclosure = false,
    showApiText = true,
    showFontAwesomeIcons = false,
    showWhatsAppIcon = false,
    trackMeta = true,
    trackPageView = true,
    wordCount,
  }) => {
    const isChanneled = React.useCallback(
      tids => {
        return articleData?.channels?.some(channel => tids.includes(channel?.tid));
      },
      [articleData?.channels],
    );

    const channels = articleData.channels ? articleData.channels : [];
    const tags = articleData.tags ? articleData.tags : [];
    const keyItems = articleData.keyItems && articleData.keyItems.map(item => item.value).filter(item => item?.length);
    const title = articleData.title;
    const isSponsored = isSponsoredArticle(articleData);
    const isMarketMovingExclusive = isChanneled([165347]);

    const onCampaignsRendered = () => {
      raptiveAdManager.setReady(true);

      // renderTickersBoxes('#article-body', true);
    };

    const generateContentWithCampaign = (blocks: ArticleBlock[]) => <ArticleBlocks blocks={blocks} />;

    const articleUrl = getCanonicalUrl(articleData);
    const tickers = getPrimaryTickers(articleData?.tickers);

    const authorName =
      articleData?.author?.firstname && articleData?.author?.lastname
        ? `${articleData?.author.firstname} ${articleData?.author.lastname}`
        : articleData?.author?.name || articleData?.name;

    const onArticleVisible = articleData => {
      setDFPTargeting(articleData);
    };

    const slideshows = articleData?.meta?.wp_post_meta?.Slideshow;

    return (
      <>
        <ArticleContentWrapper
          $useNewTemplate={true}
          className={classNames('article-content-body', { 'is-headline': articleData?.isHeadline })}
          id={`node-${articleData.nodeId}`}
        >
          {Array.isArray(slideshows) && slideshows.length > 0 && (
            <ErrorBoundary name="article-layout-main-picture-block">
              <React.Suspense fallback={<div />}>
                <ArticleImageGallery slideshows={slideshows} />
              </React.Suspense>
            </ErrorBoundary>
          )}
          {keyItems && keyItems.length > 0 && (
            <LightBlueKeyPoints className="key-points-wrapper mobile md:hidden mb-2">
              <KeyPoints
                article={{
                  nodeId: articleData.nodeId,
                  url: articleData.url,
                }}
                data={keyItems}
                dynamicKeyPoint={campaigns?.top}
                isPaywalled={
                  !isBot &&
                  !showAdvertiserDisclosure &&
                  !whitelistedAuthors.includes(authorName) &&
                  isPaywallActive &&
                  !isSponsored &&
                  !isMarketMovingExclusive
                }
                trackImpression={true}
              />
            </LightBlueKeyPoints>
          )}
          <AnalyticsContext.Provider value={{ label: 'Article Above Content' }}>
            {layout && Array.isArray(layout?.content_header?.blocks) && (
              <ArticleBlocks blocks={layout.content_header.blocks} />
            )}
          </AnalyticsContext.Provider>
          {trackPageView && (
            <ErrorBoundary name="article-layout-main-visible-page-tracker">
              <VisiblePageTracker
                article={articleData}
                executeImpressionEvent={false}
                executePageviewEvent={executePageviewEvent}
                executeReplaceHistory={executeReplaceHistory}
                meta={articleData.metaProps}
                onImpress={() => onArticleVisible(articleData)}
                trackMeta={trackMeta}
              >
                <div style={{ height: 2 }} />
              </VisiblePageTracker>
            </ErrorBoundary>
          )}

          {articleData?.isHeadline ? (
            <ArticleHeadlineContent />
          ) : (
            <>
              {!isBot &&
                !showAdvertiserDisclosure &&
                !whitelistedAuthors.includes(authorName) &&
                isPaywallActive &&
                !isSponsored &&
                !isMarketMovingExclusive && (
                  <AuthContainer
                    authMode="register"
                    contentType={getPaywallContentType(articleData)}
                    iterationStyle={paywall?.paywallStyle}
                    placement="article"
                    preventRedirect={true}
                    setShowPaywall={setShowPaywall}
                  />
                )}
              <AnalyticsContext.Provider value={{ label: 'Article In Content' }}>
                <ArticleBodyContent
                  isBot={isBot}
                  isPaywalled={
                    !isBot &&
                    !showAdvertiserDisclosure &&
                    !whitelistedAuthors.includes(authorName) &&
                    isPaywallActive &&
                    !isMarketMovingExclusive
                  }
                  isSponsored={showAdvertiserDisclosure}
                >
                  {isTemplate ? null : (
                    <>
                      {articleData.shouldDisplayedInFrame ? (
                        <React.Suspense fallback={<div />}>
                          <ErrorBoundary name="article-layout-main-article-iframe">
                            <ArticleIFrame articleBody={articleData.body} />
                          </ErrorBoundary>
                        </React.Suspense>
                      ) : (
                        <ErrorBoundary name="article-layout-main-campaign">
                          <MemoCampaign
                            articleBlocks={articleData.blocks}
                            articleBody={articleData.parsedBody}
                            articleUrl={articleUrl}
                            channels={channels}
                            contentType={articleData.type}
                            createdDate={articleData.createdAt}
                            deviceType={deviceType}
                            hasAdLight={hasAdLight}
                            inContentBlocks={layout?.in_content?.blocks}
                            isSponsored={showAdvertiserDisclosure}
                            layout={layout}
                            nodeId={articleData.nodeId}
                            onRendered={onCampaignsRendered}
                            //primis={!renderArticleImage ?? primis}
                            raptiveEnabled={raptiveEnabled}
                            strategy={campaignStrategy}
                            tags={tags}
                            ticker={campaignTicker}
                            useNewTemplate={true}
                            wordCount={wordCount}
                            {...campaignSettings}
                          >
                            {generateContentWithCampaign}
                          </MemoCampaign>
                        </ErrorBoundary>
                      )}

                      {Array.isArray(featuredTickers) && featuredTickers.length > 0 && (
                        <div className="flex md:hidden flex-col gap-4 w-full max-w-[450px] my-4 mx-auto">
                          <ArticleFeaturedTickersList
                            financials={{
                              down: Number(featuredTickers?.[0]?.analysis?.fundamentals?.down),
                              up: Number(featuredTickers[0].analysis?.fundamentals?.up),
                            }}
                            rankingData={rankingData}
                            technicals={{
                              down: Number(featuredTickers?.[0]?.analysis?.technicals?.down),
                              up: Number(featuredTickers?.[0]?.analysis?.technicals?.up),
                            }}
                            tickers={featuredTickers}
                          />
                        </div>
                      )}

                      {followUpQuestions && (
                        <div className="h-[220px] mb-2">
                          <NoFirstRender>
                            <WNSTNWidget articleID={articleData.nodeId} questions={followUpQuestions} />
                          </NoFirstRender>
                        </div>
                      )}

                      {generatedByAi && (
                        <p className="block core-block">
                          <em>
                            This content was partially produced with the help of AI tools and was reviewed and published
                            by Benzinga editors.
                          </em>
                        </p>
                      )}

                      {/* eslint-disable-next-line react/jsx-no-target-blank */}
                      {showApiText && (
                        <a
                          className="text-sm my-4"
                          href="https://www.benzinga.com/apis?utm_source=benzinga.com&amp;utm_campaign=article-bottom"
                          rel="noreferrer"
                          target="_blank"
                        >
                          Market News and Data brought to you by Benzinga APIs
                        </a>
                      )}
                      <p className="copyright">{copyright}</p>
                    </>
                  )}
                </ArticleBodyContent>
              </AnalyticsContext.Provider>
            </>
          )}

          {/* This should be moved to be in the article for longer articles */}
          {checkIfShouldLoadCalendar(articleData) && <ArticleCalendarWidget article={articleData} />}

          {title && (
            <React.Suspense fallback={<div />}>
              <div className="mb-4">
                <ShareButtons
                  buttonStyle={{
                    bgStyle: {
                      fill: '#3F83F81A',
                    },
                    iconFillColor: '#3F83F8',
                  }}
                  className="flex"
                  showFontAwesomeIcons={showFontAwesomeIcons}
                  showWhatsAppIcon={showWhatsAppIcon}
                  tickers={tickers}
                  title={title}
                  url={articleUrl}
                  utmSource="articleShare"
                />
              </div>
            </React.Suspense>
          )}

          <hr className="mb-2" />

          {trackPageView && (
            <ErrorBoundary name="article-layout-main-visible-page-tracker">
              <VisiblePageTracker
                article={articleData}
                executeImpressionEvent={false}
                executePageviewEvent={false}
                meta={articleData.metaProps}
                trackMeta={trackMeta}
              >
                <PostedIn terms={articleData.terms ?? []} variant={postedInVariant} />
              </VisiblePageTracker>
            </ErrorBoundary>
          )}

          {raptiveEnabled && (
            <React.Suspense fallback={<div className="h-[90px] w-[300px] mb-2" />}>
              <RaptiveAdPlaceholder
                className="flex items-center justify-center mb-4 min-h-[50px]"
                onlyDesktop={true}
                type="bottom"
              />
            </React.Suspense>
          )}

          <div className="hidden md:block">
            {showAccountCreationCTA && deviceType !== 'mobile' && <AccountCreationCTA utm="article-cta" />}
          </div>

          <AnalyticsContext.Provider value={{ label: 'Article Below Content' }}>
            {layout && Array.isArray(layout?.content_footer?.blocks) && (
              <ArticleBlocks blocks={layout.content_footer.blocks} />
            )}
          </AnalyticsContext.Provider>

          {!articleData?.isHeadline && relatedArticles && <RelatedArticles nodes={relatedArticles} />}
        </ArticleContentWrapper>

        {isSponsoredArticle(articleData) && (
          <TaboolaPixelHeader pageUrl={`${appEnvironment().config().url}/${articleData.canonicalPath}`} />
        )}
      </>
    );
  },
);

export const NewArticleLayoutWrapper = styled.div<{ $useNewTemplate?: boolean }>`
  ${({ $useNewTemplate }) =>
    !$useNewTemplate
      ? `
    .layout-container {
      max-width: 1080px !important;
    }
    .layout-title {
      font-family: sans-serif;
    }
    .follow-author {
      border-radius: 0px;
      font-size: 10px;
      font-weight: 700;
    }
    .key-points-wrapper {
      margin-bottom: 1rem;
      margin-top: 0.5rem;
    }
    .comments-count-button {
      color: #2ca2d1;
    }
  `
      : `

    .article-editor-box {
      max-width: 1300px;
      width: 100%;
      padding: 0 1rem;
      z-index: 2;
      color: #ffffff;

      ul {
        &.primary {
          border-radius: 4px;
        }
        li {
          background: #1e334b;
          &:hover {
            background: #253f5e;
          }
        }
      }
      a {
        color: #ffffff;
      }
    }

    .main-content-container {
      max-width: 1300px !important;
      padding: 0 1rem;
      .layout-main {
        max-width: 800px;
        width: 100%;
      }
    }
    .layout-sidebar-sticky-box {
      margin: 0 auto;
    }
  `}

  .layout-header {
    padding: 0;
  }

  .layout-title {
    &__link {
      color: #333;
    }
  }

  h2.wp-block-heading {
    font-weight: 600;
  }

  .show-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8rem;

    .show-box-text {
      height: 8rem;
      font-size: ${({ theme }) => theme.fontSize['2xl']};
    }
  }

  .read-more-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 0 1.5rem 0;
    font-size: 18px;
    text-transform: uppercase;

    a {
      box-shadow: none;
    }

    .chevron-right-icon {
      margin-left: 0.5rem;
    }
  }

  .article-submitted {
    display: inline-block;
    font-size: 14px;
    margin-bottom: 0.3rem;
  }

  .author-name {
    &:hover {
      text-decoration: underline;
    }
  }

  .video-player-wrapper {
    position: relative;
    margin-bottom: 1rem;

    &:before {
      display: block;
      content: '';
      width: 100%;
      padding-top: 43%;

      @media (max-width: 800px) {
        padding-top: calc(56.25% + 97px);
      }

      @media (max-width: 335px) {
        padding-top: calc(56.25% + 88px);
      }
    }

    &.livestream {
      &:before {
        padding-top: calc((105 / 187) * 100%);
      }
    }

    &__inner {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      height: 100%;
      overflow: hidden;
    }
  }
  .raptive-ad-placement + .wp-block-heading {
    margin-top: 1.2rem;
  }
`;

export const LightBlueKeyPointss = styled.div`
  &.key-points-wrapper {
    .key-points {
      background-color: ${({ theme }) => theme.colorPalette.blue50};
      border: unset;
      border-top: 2px solid ${({ theme }) => theme.colorPalette.blue500};
      border-bottom: 2px solid ${({ theme }) => theme.colorPalette.blue500};

      .key-points-header {
        font-size: 16px;
        color: ${({ theme }) => theme.colorPalette.blue500};
      }

      ul {
        list-style: none;
        margin-left: 10px;

        li.bullet-point {
          color: ${({ theme }) => theme.colorPalette.gray700};
          margin-bottom: 24px;
          position: relative;
          padding-left: 20px;
          font-weight: normal;

          &:last-of-type {
            margin-bottom: 0;
          }

          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 10px;
            width: 8px;
            height: 8px;
            background-color: ${({ theme }) => theme.colorPalette.blue500};
            border-radius: 50%;
            box-shadow:
              0 0 0px #3f83f81a,
              0 0 1px 5px #3f83f81a;
            transform: unset;
          }

          &:not(:last-of-type):after {
            content: '';
            position: absolute;
            left: 3.5px;
            top: 20px;
            height: calc(100% + 12px);
            width: 1px;
            background-color: ${({ theme }) => theme.colorPalette.blue500};
            opacity: 0.3;
          }

          &.bullhorn {
            &:before,
            &:after {
              content: none;
            }
          }
        }
      }
    }
  }
`;
