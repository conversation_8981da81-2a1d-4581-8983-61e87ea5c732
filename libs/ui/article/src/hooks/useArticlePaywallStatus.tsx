import { useCallback, useEffect, useState, useRef, useContext } from 'react';
import { ArticleData } from '@benzinga/article-manager';
import { IsUserPaywalledReturn, useIsUserPaywalled } from '@benzinga/user-context';
import { getPaywallType } from '../utils';
import { DeviceType } from '@benzinga/device-utils';
import { sophiManager } from '@benzinga/ads-utils';
import { SessionContext } from '@benzinga/session-context';
import { useUser } from '@benzinga/user-context';

interface UseArticlePaywallStatusReturn {
  isNotPaywalled: boolean;
  isPaywallActive: boolean;
  setIsPaywallActive: (value: boolean) => void;
  paywall: IsUserPaywalledReturn;
  headerRef: (node: HTMLDivElement | null) => void;
}

export const useArticlePaywallStatus = (
  articleData: ArticleData,
  disablePaywall?: boolean,
  deviceType?: DeviceType | null,
): UseArticlePaywallStatusReturn => {
  const session = useContext(SessionContext);
  const user = useUser();
  const isChanneled = useCallback(
    (tids: number[]) => {
      return articleData?.channels?.some(channel => tids.includes(channel?.tid));
    },
    [articleData?.channels],
  );

  const isNotPaywalled = isChanneled([165347]);
  const paywall = useIsUserPaywalled('com/read', 'unlimited-articles', getPaywallType(articleData), true);

  const [isPaywallActive, setIsPaywallActive] = useState(false);
  const [sophiDecision, setSophiDecision] = useState<{
    outcome?: {
      wallVisibility?: 'always' | 'never' | string;
      wallType?: string;
    };
  } | null>(null);
  const headerRef = useRef<HTMLDivElement | null>(null);
  const scrollHandlerRef = useRef<(() => void) | null>(null);
  //const sophiInitializedRef = useRef<boolean>(false);

  // useEffect(() => {
  //   if (sophiInitializedRef.current) return;
  //   console.log('[Sophi] Initializing Sophi');
  //   sophiManager.init();
  //   sophiInitializedRef.current = true;
  //   console.log('[Sophi] Initialization complete');
  // }, []);

  useEffect(() => {
    const isScriptLoaded = sophiManager.isLoaded();
    console.log('isScriptLoaded', isScriptLoaded);
    if (!articleData || !isScriptLoaded) return;

    const section = articleData.channels?.[0]?.name || 'news';
    console.log('[Sophi] Article section:', section);

    console.log('[Sophi] Notifying page view');
    sophiManager.notifyPageView(true, section);

    const trackingData = {
      article_author: articleData.author?.name || '',
      article_id: String(articleData.nodeId || ''),
      article_published_date: articleData.createdAt,
      article_title: articleData.title || '',
      page_section: section,
      page_type: 'article',
    };
    console.log('[Sophi] Tracking page view with data:', trackingData);
    sophiManager.trackWithData(session, 'page_view', trackingData);
    console.log('[Sophi] Page view tracking complete');
  }, [articleData, session]);

  useEffect(() => {
    const isScriptLoaded = sophiManager.isLoaded();
    if (!user || !articleData || disablePaywall || !isScriptLoaded) return;

    console.log('[Sophi] Getting paywall decision for user:', user.accessType);

    const getPaywallDecision = async () => {
      const userType = user.accessType === 'anonymous' ? 'anonymous' : 'registered';
      console.log('[Sophi] User type:', userType);

      console.log('[Sophi] Requesting decision from Sophi');
      const decision = await sophiManager.getDecision(userType);
      console.log('[Sophi] Decision received:', decision);
      setSophiDecision(decision);

      if (decision.outcome?.wallVisibility === 'always') {
        console.log('[Sophi] Decision: Show paywall');

        const section = articleData.channels?.[0]?.name || 'news';

        const wallType = decision.outcome.wallType || 'paywall';
        console.log('[Sophi] Notifying wall encounter, type:', wallType);
        sophiManager.notifyWall(wallType, section);

        const wallHitData = {
          article_id: String(articleData.nodeId || ''),
          article_title: articleData.title || '',
          section: section,
          wall_type: wallType,
        };
        console.log('[Sophi] Tracking wall hit with data:', wallHitData);
        sophiManager.trackWithData(session, 'wallhit', wallHitData);
        console.log('[Sophi] Wall hit tracking complete');
      } else {
        console.log('[Sophi] Decision: Do not show paywall');
      }
    };

    getPaywallDecision();
  }, [user, articleData, session, disablePaywall]);

  const setHeaderRef = useCallback((node: HTMLDivElement | null) => {
    if (node) {
      headerRef.current = node;
    }
  }, []);

  useEffect(() => {
    if (disablePaywall) {
      console.log('[Sophi] Paywall disabled by prop');
      setIsPaywallActive(false);
      return;
    }

    if (sophiDecision && sophiDecision.outcome) {
      console.log('[Sophi] Using Sophi decision for paywall');

      if (sophiDecision.outcome.wallVisibility === 'always') {
        console.log('[Sophi] Activating paywall based on Sophi decision');
        setIsPaywallActive(true);
        return;
      } else if (sophiDecision.outcome.wallVisibility === 'never') {
        console.log('[Sophi] Deactivating paywall based on Sophi decision');
        setIsPaywallActive(false);
        return;
      }
    }

    console.log('[Sophi] No Sophi decision, falling back to original paywall logic');
    console.log('[Sophi] Original paywall active:', paywall.active);

    // Fall back to the original paywall logic if Sophi doesn't have a decision
    if (!headerRef.current || !paywall.active) return;

    const handleScroll = () => {
      if (!headerRef.current) return;

      const navHeader = document.getElementById('navigation-header');
      const navHeaderHeight = navHeader?.offsetHeight ?? 0;

      const headerRect = headerRef.current.getBoundingClientRect();
      const headerHeight = headerRect.height;
      const scrollPosition = window.scrollY;
      const scrollThreshold = headerHeight * 0.4 + (deviceType === 'mobile' ? 0 : navHeaderHeight);

      if (scrollPosition >= scrollThreshold) {
        console.log('[Sophi] Activating paywall based on scroll position');
        setIsPaywallActive(true);
        if (scrollHandlerRef.current) {
          window.removeEventListener('scroll', scrollHandlerRef.current);
        }
      }
    };

    scrollHandlerRef.current = handleScroll;
    window.addEventListener('scroll', scrollHandlerRef.current);

    return () => {
      if (scrollHandlerRef.current) {
        window.removeEventListener('scroll', scrollHandlerRef.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [disablePaywall, paywall.active, sophiDecision]);

  console.log('sophiDecision', sophiDecision, isPaywallActive);

  return {
    headerRef: setHeaderRef,
    isNotPaywalled,
    isPaywallActive,
    paywall,
    setIsPaywallActive,
  };
};
