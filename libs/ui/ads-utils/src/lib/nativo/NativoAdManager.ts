import { runningClientSide } from '@benzinga/utils';
import { NavitoEvents, PostRelease } from './entities';

declare global {
  interface Window {
    ntv: {
      [key: string]: any;
      CONSTS: [key: string];
      CompanionAsset: () => void;
      DFP: (e) => void;
      Events: NavitoEvents;
      PostRelease: PostRelease;
      cmd: Array<() => void>;
      version: string;
      initialized: boolean;
    };
    PostRelease: PostRelease;
  }
}

interface PageTargeting {
  [key: string]: string | string[] | boolean;
}

class NativoAdManager {
  private loaded = false;
  private initialized = false;
  private disabled = false;
  private targeting: PageTargeting = {};

  public isLoaded(): boolean {
    return this.loaded;
  }

  public isDisabled(): boolean {
    return this.disabled;
  }

  public init(): void {
    try {
      if (!runningClientSide()) return;
      if (this.disabled) return;

      this.initialized = true;
      this.log('Nativo ad script initialized');

      this.injectNativoScript({
        onLoad: () => {
          this.log('Nativo ad script injected successfully');
          this.executeStart();
          this.loaded = true;
        },
      });
    } catch (error) {
      this.log('Error initializing Nativo ad script: ', error);
    }
  }

  public start(): void {
    if (!runningClientSide() || this.disabled || !this.initialized) return;
    this.executeStart();
  }

  public disable(): void {
    this.disabled = true;
  }

  public enable(): void {
    this.disabled = false;
  }

  public setTargeting(newTargeting: PageTargeting): PageTargeting {
    this.targeting = { ...this.targeting, ...(newTargeting || {}) };

    if (runningClientSide()) {
      window['ntvConfig'] = window['ntvConfig'] || {};
      window['ntvConfig'].keyValues = { ...(window['ntvConfig'].keyValues || {}), ...(this.targeting || {}) };
    }

    return this.targeting;
  }

  public getTargeting(): PageTargeting {
    return this.targeting;
  }

  public refresh(): void {
    this.loaded = false;
    this.start();
    this.log('Refreshed Nativo ads');
  }

  private executeStart(): void {
    // if (!runningClientSide() || this.disabled || this.loaded) return;
    window.ntv = window.ntv || {};
    window.ntv.cmd = window.ntv.cmd || [];
    window.ntv.cmd.push(() => {
      try {
        window.PostRelease.Start();
        this.log('Nativo PostRelease started successfully');
      } catch (error) {
        this.log('Error starting Nativo PostRelease: ', error);
      }
    });
  }

  private injectNativoScript = ({
    onError,
    onLoad,
  }: {
    onError?: () => void;
    onLoad?: () => void;
  } = {}) => {
    if (document.getElementById('nativo-script')) return;

    const scriptElement = document.createElement('script');
    scriptElement.id = 'nativo-script';
    scriptElement.src = 'https://s.ntv.io/serve/load.js';
    scriptElement.async = true;
    scriptElement.setAttribute('data-ntv-set-no-auto-start', '');
    scriptElement.type = 'text/javascript';

    if (typeof onLoad === 'function') {
      scriptElement.onload = onLoad;
    }
    scriptElement.onerror = (event: Event | string) => {
      if (typeof event === 'object' && event?.type === 'error') {
        console.error('Failed to inject Nativo ad script');
      }
      typeof onError === 'function' && onError();
    };

    document.head.appendChild(scriptElement);
  };

  private log(message: string, ...optionalParams: any[]): void {
    if (!runningClientSide()) return;
    if (new URLSearchParams(window.location.search).get('debug_nativo_ad_manager') === 'true') {
      console.log(`[Nativo Ad Manager] ${message}`, ...optionalParams);
    }
  }
}

export const nativoAdManager = new NativoAdManager();
